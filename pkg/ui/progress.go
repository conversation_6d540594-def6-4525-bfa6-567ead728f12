package ui

import (
	"fmt"
	"strings"

	"bella/pkg/copier"
	"bella/pkg/progress"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// runOperation launches the progress screen and the backend logic.
func (a *App) runOperation(cfg *copier.Config) {
	progressPageName := "progressModal"

	modal := tview.NewFlex().SetDirection(tview.FlexRow)
	modal.SetBorder(true).SetTitle("Operation in Progress")

	stageText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	progressBar := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	statsText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)

	modal.AddItem(stageText, 1, 0, false).
		AddItem(progressBar, 3, 0, false).
		AddItem(statsText, 0, 1, false)

	frame := tview.NewFrame(modal).SetBorders(0, 0, 0, 0, 0, 0)

	centeredFlex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(frame, 10, 0, true).
			AddItem(nil, 0, 1, false),
			80, 0, true).
		AddItem(nil, 0, 1, false)

	centeredFlex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		return nil
	})

	a.pages.AddPage(progressPageName, centeredFlex, true, true)
	a.app.SetFocus(centeredFlex)

	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	operationResult := make(chan error, 1)

	go func() {
		defer close(progressChan)
		operationResult <- copier.Execute(cfg)
	}()

	go func() {
		var lastSummary string
		for p := range progressChan {
			currentProgress := p
			a.app.QueueUpdateDraw(func() {
				if !currentProgress.IsFinished {
					titleColor := a.theme.colorToHex(a.theme.PrimaryText)
					stageColor := a.theme.colorToHex(a.theme.InfoColor)
					speedColor := a.theme.colorToHex(a.theme.AccentText)

					modal.SetTitle(fmt.Sprintf("[%s]Operation in Progress", titleColor))

					stageText.SetText(fmt.Sprintf("[%s]Stage: [%s]%s", stageColor, titleColor, titleCase(currentProgress.Stage)))
					progressBar.SetText(a.createProgressBar(currentProgress.PercentDone))
					stats := fmt.Sprintf(
						"[%s]Speed:[%s] %.2f MB/s\n[%s]ETA:[%s] %s\n[%s]Data:[%s] %s / %s",
						speedColor, titleColor, currentProgress.AvgSpeed,
						speedColor, titleColor, currentProgress.ETA,
						speedColor, titleColor,
						progress.HumanizeBytes(uint64(currentProgress.BytesDone)),
						progress.HumanizeBytes(uint64(currentProgress.BytesTotal)),
					)
					statsText.SetText(stats)
				}
				if currentProgress.Summary != "" {
					lastSummary = currentProgress.Summary
				}
				a.app.Sync()
			})
		}

		err := <-operationResult
		a.app.QueueUpdateDraw(func() {
			a.pages.RemovePage(progressPageName)
			a.app.Sync()
			a.showResultModal(err, lastSummary)
		})
	}()
}

// showResultModal creates the final result modal, cleanly displaying all stats.
func (a *App) showResultModal(err error, summary string) {
	resultPageName := "resultModal"
	var message string

	errorColor := a.theme.colorToHex(a.theme.ErrorColor)
	successColor := a.theme.colorToHex(a.theme.SuccessColor)
	textColor := a.theme.colorToHex(a.theme.PrimaryText)

	if err != nil {
		message = fmt.Sprintf("[%s]Operation Failed:\n\n[%s]%v", errorColor, textColor, err)
	} else {
		// Display the final summary text from the reporter.
		message = fmt.Sprintf("[%s]✔ All Stages Complete\n\n[%s]%s", successColor, textColor, summary)
	}

	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"OK"}).
		SetBackgroundColor(a.theme.ModalBg).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			if a.pages.HasPage("main") {
				// This handles GUI mode
				a.pages.RemovePage(resultPageName)
				a.pages.SwitchToPage("main")
			} else {
				// This handles CLI mode
				a.app.Stop()
			}
		})

	a.pages.AddPage(resultPageName, modal, true, true)
}

// createProgressBar is a new helper for the UI with solid background fill
func (a *App) createProgressBar(percent float64) string {
	barWidth := 40
	filled := int(percent * float64(barWidth) / 100.0)
	if filled < 0 {
		filled = 0
	}
	if filled > barWidth {
		filled = barWidth
	}

	fillColor := a.theme.colorToHex(a.theme.ProgressFill)
	emptyColor := a.theme.colorToHex(a.theme.ProgressEmpty)
	textColor := a.theme.colorToHex(a.theme.ProgressText)

	// Use solid background fill to hide underlying content as per user preference
	bar := fmt.Sprintf("[%s]%s[%s]%s", fillColor, strings.Repeat("█", filled), emptyColor, strings.Repeat("█", barWidth-filled))
	return fmt.Sprintf("%s\n[%s]%.1f%%", bar, textColor, percent)
}

// RunCLIProgress creates a minimal tview-based progress display for CLI mode
func RunCLIProgress(cfg *copier.Config) error {
	cliApp := NewApp()

	// Create a simple progress display for CLI mode
	progressPageName := "cliProgress"

	modal := tview.NewFlex().SetDirection(tview.FlexRow)
	modal.SetBorder(true).SetTitle("Bella - Operation in Progress")

	stageText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	progressBar := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)
	statsText := tview.NewTextView().SetDynamicColors(true).SetTextAlign(tview.AlignCenter)

	modal.AddItem(stageText, 1, 0, false).
		AddItem(progressBar, 3, 0, false).
		AddItem(statsText, 0, 1, false)

	// Center the modal on screen
	centeredFlex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(modal, 10, 0, true).
			AddItem(nil, 0, 1, false),
			80, 0, true).
		AddItem(nil, 0, 1, false)

	cliApp.pages.AddPage(progressPageName, centeredFlex, true, true)
	cliApp.app.SetRoot(cliApp.pages, true).SetFocus(centeredFlex)

	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	operationResult := make(chan error, 1)

	go func() {
		defer close(progressChan)
		operationResult <- copier.Execute(cfg)
	}()

	go func() {
		var lastSummary string
		for p := range progressChan {
			currentProgress := p
			cliApp.app.QueueUpdateDraw(func() {
				if !currentProgress.IsFinished {
					titleColor := cliApp.theme.colorToHex(cliApp.theme.PrimaryText)
					stageColor := cliApp.theme.colorToHex(cliApp.theme.InfoColor)
					speedColor := cliApp.theme.colorToHex(cliApp.theme.AccentText)

					modal.SetTitle(fmt.Sprintf("[%s]Bella - Operation in Progress", titleColor))
					stageText.SetText(fmt.Sprintf("[%s]Stage: [%s]%s", stageColor, titleColor, titleCase(currentProgress.Stage)))
					progressBar.SetText(cliApp.createProgressBar(currentProgress.PercentDone))
					stats := fmt.Sprintf(
						"[%s]Speed:[%s] %.2f MB/s\n[%s]ETA:[%s] %s\n[%s]Data:[%s] %s / %s",
						speedColor, titleColor, currentProgress.AvgSpeed,
						speedColor, titleColor, currentProgress.ETA,
						speedColor, titleColor,
						progress.HumanizeBytes(uint64(currentProgress.BytesDone)),
						progress.HumanizeBytes(uint64(currentProgress.BytesTotal)),
					)
					statsText.SetText(stats)
				}
				if currentProgress.Summary != "" {
					lastSummary = currentProgress.Summary
				}
				cliApp.app.Sync()
			})
		}

		err := <-operationResult
		cliApp.app.QueueUpdateDraw(func() {
			cliApp.pages.RemovePage(progressPageName)
			cliApp.app.Sync()
			cliApp.showResultModal(err, lastSummary)
		})
	}()

	return cliApp.app.Run()
}
