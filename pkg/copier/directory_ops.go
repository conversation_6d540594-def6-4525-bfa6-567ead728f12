package copier

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"

	"bella/pkg/progress"
)

// copyDirectory uses a worker pool to copy files in parallel.
func copyDirectory(cfg *Config) error {
	log.Printf("Preparing to recursively copy directory %s to %s with %d threads\n", cfg.Input, cfg.Output, cfg.Threads)

	// --- STAGE 1: SCANNING and PLANNING ---
	type fileJob struct {
		srcPath  string
		destPath string
		size     int64
		mode     os.FileMode
	}
	var fileJobs []fileJob
	var totalSize int64

	err := filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			return err
		}
		destPath := filepath.Join(cfg.Output, relPath)

		if d.Is<PERSON>ir() {
			// Pre-create all directories sequentially
			if err := os.MkdirAll(destPath, 0755); err != nil {
				return err
			}
			// Apply metadata to directories if preserving attributes
			if cfg.PreserveAttributes {
				if err := applyMetadata(path, destPath); err != nil {
					log.Printf("Warning: failed to apply metadata to directory '%s': %v", destPath, err)
				}
			}
			return nil
		}

		info, err := d.Info()
		if err != nil {
			return err
		}

		job := fileJob{srcPath: path, destPath: destPath, size: info.Size(), mode: info.Mode()}
		fileJobs = append(fileJobs, job)
		totalSize += info.Size()
		return nil
	})
	if err != nil {
		return fmt.Errorf("failed to scan source directory: %w", err)
	}

	// --- STAGE 2: PARALLEL COPYING ---
	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("copying directory", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	jobs := make(chan fileJob, len(fileJobs))
	results := make(chan error, len(fileJobs))
	var wg sync.WaitGroup
	var copiedSize int64

	// Start worker goroutines
	for w := 1; w <= cfg.Threads; w++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for job := range jobs {
				// The call is now simpler as the logic is in the helper.
				err := copyFileForWorker(job.srcPath, job.destPath, cfg, &copiedSize, totalSize, reporter)
				results <- err // Send result back, nil on success
			}
		}(w)
	}

	// Feed jobs to workers
	for _, job := range fileJobs {
		jobs <- job
	}
	close(jobs)

	// Wait for results
	var firstErr error
	for i := 0; i < len(fileJobs); i++ {
		err := <-results
		if err != nil && firstErr == nil {
			firstErr = err // Capture the first error
		}
	}
	wg.Wait() // Ensure all workers have finished

	if firstErr != nil {
		return firstErr // Return the first error encountered
	}

	// FIX STARTS HERE: Only finish the reporter if there's no verification stage.
	// If verification is next, we just transition stages.

	if cfg.Progress && reporter != nil {
		// Ensure the copy progress bar reaches 100% before transitioning.
		reporter.Update(totalSize, totalSize)
	}
	log.Println("Directory copy stage completed.")

	// --- STAGE 3: SEQUENTIAL VERIFICATION (if requested) ---
	if cfg.Verify {
		log.Println("Starting directory verification stage.")
		if cfg.Progress && reporter != nil {
			// Transition to the new stage and reset progress for the UI.
			reporter.SetStage("verifying directory")
			reporter.Update(0, totalSize)
		}

		var verifiedSize int64
		for _, job := range fileJobs {
			verifyCfg := *cfg
			verifyCfg.Input = job.srcPath
			verifyCfg.Output = job.destPath
			verifyCfg.Progress = false // Sub-operation doesn't need its own reporter

			if err := doVerify(&verifyCfg); err != nil {
				return fmt.Errorf("verification failed for file '%s': %w", job.destPath, err)
			}
			verifiedSize += job.size
			if cfg.Progress && reporter != nil {
				reporter.Update(verifiedSize, totalSize)
			}
		}

		// Now we can finish the *entire* operation.
		if cfg.Progress && reporter != nil {
			reporter.Finish(verifiedSize)
		}
		log.Println("Directory verification stage completed successfully.")
	} else {
		// If there was no verification, finish the operation after the copy stage.
		if cfg.Progress && reporter != nil {
			reporter.Finish(totalSize)
		}
	}

	return nil
}
