package copier

import (
	"fmt"
	"os"
)

func Execute(cfg *Config) error {
	if cfg.DryRun {
		fmt.Printf("Dry run: would perform operation with config: %+v\n", cfg)
		return nil
	}

	// Automatically detect directory copy.
	if cfg.Operation == OpCopy {
		info, err := os.Stat(cfg.Input)
		if err != nil {
			return fmt.Errorf("could not access input '%s': %w", cfg.Input, err)
		}
		if info.IsDir() {
			return copyDirectory(cfg)
		}
		// It's a file, proceed as before.
		return copyFile(cfg)
	}

	// Handle other operations
	switch cfg.Operation {
	case OpWipe:
		return doWipe(cfg)
	case OpVerify:
		return doVerify(cfg)
	default:
		return fmt.Errorf("no operation specified or recognized")
	}
}
