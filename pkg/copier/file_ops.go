package copier

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
	"syscall"
)

// promptForFileAction prompts the user when a file already exists.
func promptForFileAction(outputPath string, isUI bool) (string, error) {
	if isUI {
		// UI mode has its own dialog, but we can default to overwrite for its logic path.
		return "overwrite", nil
	}

	fmt.Printf("File '%s' already exists.\n", outputPath)
	fmt.Print("Choose action: (o)verwrite, (a)ppend, (v)erify only, (c)ancel [o/a/v/c]: ")

	reader := bufio.NewReader(os.Stdin)
	response, err := reader.ReadString('\n')
	if err != nil {
		return "", fmt.Errorf("failed to read user input: %w", err)
	}

	response = strings.ToLower(strings.TrimSpace(response))
	switch response {
	case "o", "overwrite":
		return "overwrite", nil
	case "a", "append":
		return "append", nil
	case "v", "verify":
		return "verify", nil
	case "c", "cancel":
		return "cancel", nil
	default:
		return "cancel", fmt.Errorf("invalid choice: %s", response)
	}
}

// isAllZeros checks if a byte slice contains only zeros.
func isAllZeros(data []byte) bool {
	for _, b := range data {
		if b != 0 {
			return false
		}
	}
	return true
}

// createFileWithMetadata creates a file and immediately applies the source file's metadata
func createFileWithMetadata(srcPath, destPath string, flags int, preserveAttributes bool) (*os.File, error) {
	// Get source file metadata first
	var srcInfo os.FileInfo
	var err error
	if preserveAttributes {
		srcInfo, err = os.Stat(srcPath)
		if err != nil {
			return nil, fmt.Errorf("failed to stat source for metadata: %w", err)
		}
	}

	// Create the file with appropriate permissions
	perm := os.FileMode(0666)
	if preserveAttributes && srcInfo != nil {
		perm = srcInfo.Mode()
	}

	file, err := os.OpenFile(destPath, flags, perm)
	if err != nil {
		return nil, err
	}

	// Apply metadata immediately after creation if preserving attributes
	if preserveAttributes && srcInfo != nil {
		if err := applyMetadata(srcPath, destPath); err != nil {
			log.Printf("Warning: failed to apply metadata to '%s': %v", destPath, err)
		}
	}

	return file, nil
}

// applyMetadata applies source file metadata (permissions, ownership) to destination file
func applyMetadata(srcPath, destPath string) error {
	// Get source file metadata
	info, err := os.Stat(srcPath)
	if err != nil {
		return fmt.Errorf("failed to stat source for metadata: %w", err)
	}

	// 1. Set Permissions
	if err := os.Chmod(destPath, info.Mode()); err != nil {
		log.Printf("Warning: could not set permissions on %s: %v", destPath, err)
	}

	// 2. Set Ownership (UID/GID)
	if stat, ok := info.Sys().(*syscall.Stat_t); ok {
		uid := int(stat.Uid)
		gid := int(stat.Gid)
		if err := os.Chown(destPath, uid, gid); err != nil {
			// This will likely fail if not run as root, which is expected.
			// We log it as a warning rather than a fatal error.
			log.Printf("Warning: could not set ownership on %s (run with sudo?): %v", destPath, err)
		}
	}

	return nil
}
